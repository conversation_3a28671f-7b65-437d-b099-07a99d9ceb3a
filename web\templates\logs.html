<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请求日志 - TurnsAPI</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8" x-data="logsManagement()">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">请求日志</h1>
                <p class="text-gray-600">查看和分析API请求日志</p>
            </div>
            <div class="flex space-x-4">
                <a href="/dashboard" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    返回仪表板
                </a>
                <button @click="refreshLogs()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    刷新数据
                </button>
            </div>
        </div>

        <!-- Total Tokens Stats -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-gray-800">Token使用统计</h2>
                <button @click="refreshTokenStats()" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition duration-200">
                    刷新Token统计
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" x-text="totalTokensUsed.toLocaleString()"></div>
                    <div class="text-sm text-gray-600">总Token数</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" x-text="successTokensUsed.toLocaleString()"></div>
                    <div class="text-sm text-gray-600">成功Token数</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600" x-text="avgTokensPerRequest"></div>
                    <div class="text-sm text-gray-600">平均Token/请求</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600" x-text="tokenSuccessRate + '%'"></div>
                    <div class="text-sm text-gray-600">Token成功率</div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总请求数</p>
                        <p class="text-2xl font-semibold text-gray-900" x-text="totalRequests"></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">成功请求</p>
                        <p class="text-2xl font-semibold text-gray-900" x-text="successRequests"></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">失败请求</p>
                        <p class="text-2xl font-semibold text-gray-900" x-text="errorRequests"></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">平均响应时间</p>
                        <p class="text-2xl font-semibold text-gray-900" x-text="avgResponseTime + 'ms'"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-6 text-gray-800">数据可视化</h2>

            <!-- 第一行图表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- 请求状态分布饼图 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-4 text-center text-gray-700">请求状态分布</h3>
                    <div id="statusChart" class="flex justify-center items-center min-h-[250px]"></div>
                </div>
                <!-- 模型使用统计柱状图 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-4 text-center text-gray-700">模型使用统计</h3>
                    <div id="modelChart" class="min-h-[250px] flex justify-center items-center"></div>
                </div>
            </div>

            <!-- 第二行图表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Token使用趋势图 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-4 text-center text-gray-700">Token使用趋势</h3>
                    <div id="tokenTrendChart" class="min-h-[250px] flex justify-center items-center"></div>
                </div>
                <!-- 各分组Token数统计 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-4 text-center text-gray-700">各分组Token数统计</h3>
                    <div id="groupTokenChart" class="min-h-[250px] flex justify-center items-center"></div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">代理密钥</label>
                    <select x-model="filters.proxyKeyName" @change="applyFilters()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">所有密钥</option>
                        <template x-for="key in proxyKeys" :key="key">
                            <option :value="key" x-text="key"></option>
                        </template>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">分组</label>
                    <select x-model="filters.providerGroup" @change="applyFilters()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">所有分组</option>
                        <template x-for="group in providerGroups" :key="group">
                            <option :value="group" x-text="group"></option>
                        </template>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">模型</label>
                    <select x-model="filters.model" @change="applyFilters()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">所有模型</option>
                        <template x-for="model in models" :key="model">
                            <option :value="model" x-text="model"></option>
                        </template>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                    <select x-model="filters.status" @change="applyFilters()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">所有状态</option>
                        <option value="200">成功 (200)</option>
                        <option value="error">错误 (非200)</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">请求类型</label>
                    <select x-model="filters.stream" @change="applyFilters()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">所有类型</option>
                        <option value="true">流式</option>
                        <option value="false">非流式</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Logs Table -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">请求日志</h2>
                <div class="flex items-center space-x-4">
                    <!-- 批量操作按钮 -->
                    <div x-show="selectedLogs.length > 0" class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">已选择 <span x-text="selectedLogs.length"></span> 条</span>
                        <button @click="deleteSelectedLogs()"
                                class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition duration-200">
                            删除选中
                        </button>
                    </div>
                    <!-- 导出和清空按钮 -->
                    <div class="flex items-center space-x-2">
                        <button @click="exportLogs()"
                                class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition duration-200">
                            导出日志
                        </button>
                        <button @click="clearAllLogs()"
                                class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition duration-200">
                            清空所有
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        共 <span x-text="totalCount"></span> 条记录
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox"
                                       @change="toggleSelectAll()"
                                       :checked="isAllSelected()"
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代理密钥</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分组</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模型</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">响应时间</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Token数</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户端IP</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">错误</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="log in logs" :key="log.id">
                            <tr>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <input type="checkbox"
                                           :value="log.id"
                                           @change="toggleLogSelection(log.id)"
                                           :checked="selectedLogs.includes(log.id)"
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatDate(log.created_at)"></td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900" x-text="log.proxy_key_name || 'Unknown'"></div>
                                        <div class="text-gray-500 text-xs" x-text="log.proxy_key_id || 'unknown'"></div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                                          x-text="log.provider_group || '-'"></span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="log.model"></td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span :class="log.status_code === 200 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                          x-text="log.status_code"></span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span :class="log.is_stream ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'"
                                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                          x-text="log.is_stream ? '流式' : '普通'"></span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="log.duration + 'ms'"></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="flex items-center space-x-1">
                                        <span x-text="log.tokens_used || '-'"></span>
                                        <span x-show="log.tokens_estimated"
                                              class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                                              title="使用备用估算方法计算的Token数">
                                            估算
                                        </span>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" x-text="log.client_ip || '-'"></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <button @click="viewLogDetail(log.id)" class="text-indigo-600 hover:text-indigo-900">查看详情</button>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-red-600 max-w-xs truncate"
                                    :title="log.error" x-text="log.error || '-'"></td>    
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6 flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    显示 <span x-text="(currentPage - 1) * pageSize + 1"></span> 到 <span x-text="Math.min(currentPage * pageSize, totalCount)"></span> 条，共 <span x-text="totalCount"></span> 条记录
                </div>
                <div class="flex space-x-2">
                    <button @click="previousPage()" :disabled="currentPage === 1"
                            class="px-3 py-1 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                        上一页
                    </button>
                    <span class="px-3 py-1 bg-blue-500 text-white rounded-lg" x-text="currentPage"></span>
                    <button @click="nextPage()" :disabled="currentPage >= totalPages"
                            class="px-3 py-1 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                        下一页
                    </button>
                </div>
            </div>
        </div>

        <!-- Log Detail Modal -->
        <div x-show="showDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
            <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">请求详情</h3>
                        <button @click="showDetailModal = false" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div x-show="logDetail" class="space-y-6">
                        <!-- Basic Info -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">请求时间</label>
                                <p class="mt-1 text-sm text-gray-900" x-text="logDetail ? formatDate(logDetail.created_at) : ''"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">代理密钥</label>
                                <div class="mt-1">
                                    <p class="text-sm font-medium text-gray-900" x-text="logDetail ? logDetail.proxy_key_name : ''"></p>
                                    <p class="text-xs text-gray-500" x-text="logDetail ? logDetail.proxy_key_id : ''"></p>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">提供商分组</label>
                                <p class="mt-1 text-sm text-gray-900" x-text="logDetail ? logDetail.provider_group : ''"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">API_KEY</label>
                                <p class="mt-1 text-sm text-gray-900 font-mono" x-text="logDetail ? logDetail.openrouter_key : ''"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">模型</label>
                                <p class="mt-1 text-sm text-gray-900" x-text="logDetail ? logDetail.model : ''"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">状态码</label>
                                <p class="mt-1 text-sm" :class="logDetail && logDetail.status_code === 200 ? 'text-green-600' : 'text-red-600'" 
                                   x-text="logDetail ? logDetail.status_code : ''"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">请求类型</label>
                                <p class="mt-1 text-sm text-gray-900" x-text="logDetail ? (logDetail.is_stream ? '流式' : '普通') : ''"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">响应时间</label>
                                <p class="mt-1 text-sm text-gray-900" x-text="logDetail ? logDetail.duration + 'ms' : ''"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Token使用量</label>
                                <div class="mt-1 flex items-center space-x-2">
                                    <p class="text-sm text-gray-900" x-text="logDetail ? (logDetail.tokens_used || '未知') : ''"></p>
                                    <span x-show="logDetail && logDetail.tokens_estimated"
                                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                                          title="使用备用估算方法计算的Token数">
                                        备用估算
                                    </span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">客户端IP</label>
                                <p class="mt-1 text-sm text-gray-900" x-text="logDetail ? (logDetail.client_ip || '未知') : ''"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">错误信息</label>
                                <p class="mt-1 text-sm text-red-600" x-text="logDetail ? (logDetail.error || '无') : ''"></p>
                            </div>
                        </div>

                        <!-- Request Body -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">请求内容</label>
                            <div class="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                                <pre class="text-sm text-gray-900 whitespace-pre-wrap" x-text="logDetail ? formatJSON(logDetail.request_body) : ''"></pre>
                            </div>
                        </div>

                        <!-- Response Body -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">响应内容</label>
                            <div class="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                                <pre class="text-sm text-gray-900 whitespace-pre-wrap" x-text="logDetail ? formatResponse(logDetail.response_body) : ''"></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Modal -->
        <div x-show="showConfirmModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mt-2" x-text="confirmTitle"></h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500" x-text="confirmMessage"></p>
                    </div>
                    <div class="items-center px-4 py-3">
                        <button @click="confirmAction()"
                                class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300">
                            确认
                        </button>
                        <button @click="showConfirmModal = false"
                                class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function logsManagement() {
            return {
                logs: [],
                logDetail: null,
                showDetailModal: false,
                showConfirmModal: false,
                confirmTitle: '',
                confirmMessage: '',
                confirmCallback: null,
                selectedLogs: [],
                currentPage: 1,
                pageSize: 20,
                totalCount: 0,
                totalRequests: 0,
                successRequests: 0,
                errorRequests: 0,
                avgResponseTime: 0,
                totalTokensUsed: 0,
                successTokensUsed: 0,
                avgTokensPerRequest: 0,
                tokenSuccessRate: 0,
                proxyKeys: [],
                providerGroups: [],
                models: [],
                filters: {
                    proxyKeyName: '',
                    providerGroup: '',
                    model: '',
                    status: '',
                    stream: ''
                },

                async init() {
                    await this.loadLogs();
                    await this.loadStats();
                    await this.loadTokenStats();
                    // 延迟初始化图表，确保数据已加载
                    setTimeout(() => {
                        this.initCharts();
                    }, 100);
                },

                get totalPages() {
                    return Math.ceil(this.totalCount / this.pageSize);
                },

                async loadLogs() {
                    try {
                        const params = new URLSearchParams({
                            limit: this.pageSize,
                            offset: (this.currentPage - 1) * this.pageSize
                        });

                        if (this.filters.proxyKeyName) params.append('proxy_key_name', this.filters.proxyKeyName);
                        if (this.filters.providerGroup) params.append('provider_group', this.filters.providerGroup);
                        if (this.filters.model) params.append('model', this.filters.model);
                        if (this.filters.status) params.append('status', this.filters.status);
                        if (this.filters.stream) params.append('stream', this.filters.stream);

                        const response = await fetch(`/admin/logs?${params}`);
                        const data = await response.json();

                        if (data.success) {
                            this.logs = data.logs || [];
                            this.totalCount = data.total_count || 0;
                            this.selectedLogs = []; // 清空选中状态
                            this.extractFilters();
                        } else {
                            console.error('Failed to load logs:', data.error);
                        }
                    } catch (error) {
                        console.error('Error loading logs:', error);
                    }
                },

                async loadStats() {
                    try {
                        const [proxyKeyStatsResponse, modelStatsResponse] = await Promise.all([
                            fetch('/admin/logs/stats/api-keys'),
                            fetch('/admin/logs/stats/models')
                        ]);

                        const proxyKeyStats = await proxyKeyStatsResponse.json();
                        const modelStats = await modelStatsResponse.json();

                        if (proxyKeyStats.success) {
                            const stats = proxyKeyStats.stats || [];
                            this.totalRequests = stats.reduce((sum, stat) => sum + stat.total_requests, 0);
                            this.successRequests = stats.reduce((sum, stat) => sum + stat.success_requests, 0);
                            this.errorRequests = stats.reduce((sum, stat) => sum + stat.error_requests, 0);
                            this.avgResponseTime = Math.round(stats.reduce((sum, stat) => sum + stat.avg_duration, 0) / stats.length) || 0;
                        }
                    } catch (error) {
                        console.error('Error loading stats:', error);
                    }
                },

                extractFilters() {
                    const proxyKeys = new Set();
                    const providerGroups = new Set();
                    const models = new Set();

                    this.logs.forEach(log => {
                        if (log.proxy_key_name) {
                            proxyKeys.add(log.proxy_key_name);
                        }
                        if (log.provider_group) {
                            providerGroups.add(log.provider_group);
                        }
                        if (log.model) {
                            models.add(log.model);
                        }
                    });

                    this.proxyKeys = Array.from(proxyKeys).sort();
                    this.providerGroups = Array.from(providerGroups).sort();
                    this.models = Array.from(models).sort();
                },

                applyFilters() {
                    this.currentPage = 1;
                    this.loadLogs();
                },

                async viewLogDetail(id) {
                    try {
                        const response = await fetch(`/admin/logs/${id}`);
                        const data = await response.json();

                        if (data.success) {
                            this.logDetail = data.log;
                            this.showDetailModal = true;
                        } else {
                            console.error('Failed to load log detail:', data.error);
                        }
                    } catch (error) {
                        console.error('Error loading log detail:', error);
                    }
                },

                async refreshLogs() {
                    await this.loadLogs();
                    await this.loadStats();
                    await this.loadTokenStats();
                    // 延迟更新图表，确保数据已加载
                    setTimeout(() => {
                        this.updateCharts();
                    }, 100);
                },

                async loadTokenStats() {
                    try {
                        const response = await fetch('/admin/logs/stats/tokens');
                        const data = await response.json();

                        if (data.success && data.stats) {
                            this.totalTokensUsed = data.stats.total_tokens || 0;
                            this.successTokensUsed = data.stats.success_tokens || 0;
                            this.avgTokensPerRequest = data.stats.success_requests > 0 ?
                                Math.round(this.successTokensUsed / data.stats.success_requests) : 0;
                            this.tokenSuccessRate = data.stats.total_requests > 0 ?
                                Math.round((data.stats.success_requests / data.stats.total_requests) * 100) : 0;
                        }
                    } catch (error) {
                        console.error('Error loading token stats:', error);
                    }
                },

                refreshTokenStats() {
                    this.loadTokenStats();
                },

                previousPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.loadLogs();
                    }
                },

                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                        this.loadLogs();
                    }
                },

                formatDate(dateString) {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    return date.toLocaleString('zh-CN');
                },

                formatJSON(jsonString) {
                    if (!jsonString) return '';
                    try {
                        const obj = JSON.parse(jsonString);
                        return JSON.stringify(obj, null, 2);
                    } catch (e) {
                        return jsonString;
                    }
                },

                formatResponse(responseString) {
                    if (!responseString) return '无响应内容';

                    // 如果是流式响应，显示前几行
                    if (responseString.includes('data: ')) {
                        const lines = responseString.split('\n').slice(0, 10);
                        return lines.join('\n') + (responseString.split('\n').length > 10 ? '\n...(更多内容)' : '');
                    }

                    // 尝试格式化JSON
                    try {
                        const obj = JSON.parse(responseString);
                        return JSON.stringify(obj, null, 2);
                    } catch (e) {
                        return responseString;
                    }
                },

                // 批量选择相关方法
                toggleLogSelection(logId) {
                    const index = this.selectedLogs.indexOf(logId);
                    if (index > -1) {
                        this.selectedLogs.splice(index, 1);
                    } else {
                        this.selectedLogs.push(logId);
                    }
                },

                toggleSelectAll() {
                    if (this.isAllSelected()) {
                        this.selectedLogs = [];
                    } else {
                        this.selectedLogs = this.logs.map(log => log.id);
                    }
                },

                isAllSelected() {
                    return this.logs.length > 0 && this.selectedLogs.length === this.logs.length;
                },

                // 删除选中的日志
                deleteSelectedLogs() {
                    if (this.selectedLogs.length === 0) {
                        alert('请先选择要删除的日志');
                        return;
                    }

                    this.confirmTitle = '确认删除';
                    this.confirmMessage = `确定要删除选中的 ${this.selectedLogs.length} 条日志吗？此操作不可撤销。`;
                    this.confirmCallback = this.performDeleteSelected;
                    this.showConfirmModal = true;
                },

                async performDeleteSelected() {
                    try {
                        const response = await fetch('/admin/logs/batch', {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                ids: this.selectedLogs
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            alert(`成功删除 ${data.deleted_count} 条日志`);
                            this.selectedLogs = [];
                            this.loadLogs();
                            this.loadStats();
                        } else {
                            alert('删除失败: ' + data.error);
                        }
                    } catch (error) {
                        console.error('Error deleting logs:', error);
                        alert('删除失败: ' + error.message);
                    }

                    this.showConfirmModal = false;
                },

                // 清空所有日志
                clearAllLogs() {
                    this.confirmTitle = '确认清空';
                    this.confirmMessage = '确定要清空所有日志吗？此操作将删除所有日志记录，不可撤销。';
                    this.confirmCallback = this.performClearAll;
                    this.showConfirmModal = true;
                },

                async performClearAll() {
                    try {
                        const response = await fetch('/admin/logs/clear', {
                            method: 'DELETE'
                        });

                        const data = await response.json();

                        if (data.success) {
                            alert(`成功清空所有日志，删除了 ${data.deleted_count} 条记录`);
                            this.selectedLogs = [];
                            this.loadLogs();
                            this.loadStats();
                        } else {
                            alert('清空失败: ' + data.error);
                        }
                    } catch (error) {
                        console.error('Error clearing logs:', error);
                        alert('清空失败: ' + error.message);
                    }

                    this.showConfirmModal = false;
                },

                // 导出日志
                async exportLogs() {
                    try {
                        const params = new URLSearchParams();
                        if (this.filters.proxyKeyName) params.append('proxy_key_name', this.filters.proxyKeyName);
                        if (this.filters.providerGroup) params.append('provider_group', this.filters.providerGroup);
                        if (this.filters.model) params.append('model', this.filters.model);
                        if (this.filters.status) params.append('status', this.filters.status);
                        if (this.filters.stream) params.append('stream', this.filters.stream);
                        params.append('format', 'csv');

                        const url = `/admin/logs/export?${params}`;

                        // 创建一个隐藏的链接来触发下载
                        const link = document.createElement('a');
                        link.href = url;
                        link.style.display = 'none';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    } catch (error) {
                        console.error('Error exporting logs:', error);
                        alert('导出失败: ' + error.message);
                    }
                },

                // 确认操作
                confirmAction() {
                    if (this.confirmCallback) {
                        this.confirmCallback();
                    }
                },

                // 初始化图表
                initCharts() {
                    this.createStatusChart();
                    this.createModelChart();
                    this.createTokenTrendChart();
                    this.createGroupTokenChart();
                },

                // 更新图表
                updateCharts() {
                    this.createStatusChart();
                    this.createModelChart();
                    this.createTokenTrendChart();
                    this.createGroupTokenChart();
                },

                // 创建状态分布饼图
                createStatusChart() {
                    const container = d3.select("#statusChart");
                    container.selectAll("*").remove();

                    const data = [
                        { label: "成功", value: this.successRequests, color: "#10B981" },
                        { label: "失败", value: this.errorRequests, color: "#EF4444" }
                    ].filter(d => d.value > 0); // 只显示有数据的部分

                    if (data.length === 0) {
                        container.append("div")
                            .style("display", "flex")
                            .style("align-items", "center")
                            .style("justify-content", "center")
                            .style("height", "200px")
                            .style("color", "#9CA3AF")
                            .style("font-size", "14px")
                            .text("暂无数据");
                        return;
                    }

                    const containerWidth = 400;
                    const containerHeight = 250;
                    const radius = 70;

                    const svg = container.append("svg")
                        .attr("width", containerWidth)
                        .attr("height", containerHeight)
                        .style("overflow", "visible")
                        .style("margin", "0 auto")
                        .style("display", "block");

                    const g = svg.append("g")
                        .attr("transform", `translate(${radius + 30},${containerHeight/2})`);

                    const pie = d3.pie()
                        .value(d => d.value)
                        .sort(null)
                        .padAngle(0.02);

                    const arc = d3.arc()
                        .innerRadius(25)
                        .outerRadius(radius);

                    const arcs = g.selectAll(".arc")
                        .data(pie(data))
                        .enter().append("g")
                        .attr("class", "arc");

                    // 添加路径动画
                    arcs.append("path")
                        .attr("d", arc)
                        .attr("fill", d => d.data.color)
                        .attr("stroke", "white")
                        .attr("stroke-width", 2)
                        .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")
                        .transition()
                        .duration(1000)
                        .attrTween("d", function(d) {
                            const interpolate = d3.interpolate({startAngle: 0, endAngle: 0}, d);
                            return function(t) {
                                return arc(interpolate(t));
                            };
                        });

                    // 添加百分比标签
                    arcs.append("text")
                        .attr("transform", d => `translate(${arc.centroid(d)})`)
                        .attr("text-anchor", "middle")
                        .style("font-size", "12px")
                        .style("fill", "white")
                        .style("font-weight", "bold")
                        .text(d => {
                            const total = data.reduce((sum, item) => sum + item.value, 0);
                            const percentage = Math.round((d.data.value / total) * 100);
                            return `${percentage}%`;
                        });

                    // 添加图例
                    const legend = svg.append("g")
                        .attr("transform", `translate(${radius * 2 + 70}, 30)`);

                    data.forEach((d, i) => {
                        const legendRow = legend.append("g")
                            .attr("transform", `translate(0, ${i * 25})`);

                        legendRow.append("circle")
                            .attr("cx", 8)
                            .attr("cy", 8)
                            .attr("r", 6)
                            .attr("fill", d.color);

                        legendRow.append("text")
                            .attr("x", 20)
                            .attr("y", 8)
                            .attr("dy", "0.35em")
                            .style("font-size", "12px")
                            .style("fill", "#374151")
                            .text(`${d.label}: ${d.value.toLocaleString()}`);
                    });

                    // 添加中心文字
                    g.append("text")
                        .attr("text-anchor", "middle")
                        .attr("dy", "-0.5em")
                        .style("font-size", "14px")
                        .style("font-weight", "bold")
                        .style("fill", "#374151")
                        .text("总请求");

                    g.append("text")
                        .attr("text-anchor", "middle")
                        .attr("dy", "1em")
                        .style("font-size", "16px")
                        .style("font-weight", "bold")
                        .style("fill", "#1F2937")
                        .text(this.totalRequests.toLocaleString());
                },

                // 创建模型使用统计柱状图
                createModelChart() {
                    const container = d3.select("#modelChart");
                    container.selectAll("*").remove();

                    // 统计模型使用情况
                    const modelStats = {};
                    this.logs.forEach(log => {
                        if (log.model) {
                            modelStats[log.model] = (modelStats[log.model] || 0) + 1;
                        }
                    });

                    const data = Object.entries(modelStats)
                        .map(([model, count]) => ({
                            model: model.length > 15 ? model.substring(0, 15) + '...' : model,
                            fullModel: model,
                            count
                        }))
                        .sort((a, b) => b.count - a.count)
                        .slice(0, 6); // 只显示前6个

                    if (data.length === 0) {
                        container.append("div")
                            .style("display", "flex")
                            .style("align-items", "center")
                            .style("justify-content", "center")
                            .style("height", "200px")
                            .style("color", "#9CA3AF")
                            .style("font-size", "14px")
                            .text("暂无数据");
                        return;
                    }

                    const margin = { top: 20, right: 20, bottom: 70, left: 50 };
                    const width = 350 - margin.left - margin.right;
                    const height = 220 - margin.top - margin.bottom;

                    const svg = container.append("svg")
                        .attr("width", width + margin.left + margin.right)
                        .attr("height", height + margin.top + margin.bottom)
                        .style("overflow", "visible")
                        .style("margin", "0 auto")
                        .style("display", "block");

                    const g = svg.append("g")
                        .attr("transform", `translate(${margin.left},${margin.top})`);

                    const x = d3.scaleBand()
                        .domain(data.map(d => d.model))
                        .range([0, width])
                        .padding(0.2);

                    const y = d3.scaleLinear()
                        .domain([0, d3.max(data, d => d.count)])
                        .nice()
                        .range([height, 0]);

                    // 创建渐变
                    const gradient = svg.append("defs")
                        .append("linearGradient")
                        .attr("id", "barGradient")
                        .attr("gradientUnits", "userSpaceOnUse")
                        .attr("x1", 0).attr("y1", height)
                        .attr("x2", 0).attr("y2", 0);

                    gradient.append("stop")
                        .attr("offset", "0%")
                        .attr("stop-color", "#3B82F6");

                    gradient.append("stop")
                        .attr("offset", "100%")
                        .attr("stop-color", "#1D4ED8");

                    // 添加柱状图
                    g.selectAll(".bar")
                        .data(data)
                        .enter().append("rect")
                        .attr("class", "bar")
                        .attr("x", d => x(d.model))
                        .attr("width", x.bandwidth())
                        .attr("y", height)
                        .attr("height", 0)
                        .attr("fill", "url(#barGradient)")
                        .attr("rx", 4)
                        .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")
                        .transition()
                        .duration(1000)
                        .delay((d, i) => i * 100)
                        .attr("y", d => y(d.count))
                        .attr("height", d => height - y(d.count));

                    // 添加数值标签
                    g.selectAll(".label")
                        .data(data)
                        .enter().append("text")
                        .attr("class", "label")
                        .attr("x", d => x(d.model) + x.bandwidth() / 2)
                        .attr("y", d => y(d.count) - 8)
                        .attr("text-anchor", "middle")
                        .style("font-size", "11px")
                        .style("font-weight", "bold")
                        .style("fill", "#374151")
                        .style("opacity", 0)
                        .text(d => d.count)
                        .transition()
                        .duration(1000)
                        .delay((d, i) => i * 100 + 500)
                        .style("opacity", 1);

                    // X轴
                    g.append("g")
                        .attr("transform", `translate(0,${height})`)
                        .call(d3.axisBottom(x))
                        .selectAll("text")
                        .style("font-size", "10px")
                        .style("fill", "#6B7280")
                        .attr("transform", "rotate(-45)")
                        .style("text-anchor", "end");

                    // Y轴
                    g.append("g")
                        .call(d3.axisLeft(y).ticks(5))
                        .selectAll("text")
                        .style("font-size", "10px")
                        .style("fill", "#6B7280");

                    // 添加网格线
                    g.append("g")
                        .attr("class", "grid")
                        .call(d3.axisLeft(y)
                            .ticks(5)
                            .tickSize(-width)
                            .tickFormat("")
                        )
                        .style("stroke-dasharray", "3,3")
                        .style("opacity", 0.3);
                },

                // 创建Token使用趋势图
                createTokenTrendChart() {
                    const container = d3.select("#tokenTrendChart");
                    container.selectAll("*").remove();

                    // 按日期统计token使用
                    const tokenByDate = {};
                    this.logs.forEach(log => {
                        if (log.tokens_used && log.tokens_used > 0 && log.created_at) {
                            const date = new Date(log.created_at).toISOString().split('T')[0]; // YYYY-MM-DD格式
                            tokenByDate[date] = (tokenByDate[date] || 0) + log.tokens_used;
                        }
                    });

                    const data = Object.entries(tokenByDate)
                        .map(([date, tokens]) => ({ date: new Date(date), tokens }))
                        .sort((a, b) => a.date - b.date)
                        .slice(-7); // 最近7天

                    if (data.length === 0) {
                        container.append("div")
                            .style("display", "flex")
                            .style("align-items", "center")
                            .style("justify-content", "center")
                            .style("height", "200px")
                            .style("color", "#9CA3AF")
                            .style("font-size", "14px")
                            .text("暂无Token使用数据");
                        return;
                    }

                    const margin = { top: 20, right: 20, bottom: 50, left: 60 };
                    const width = 350 - margin.left - margin.right;
                    const height = 220 - margin.top - margin.bottom;

                    const svg = container.append("svg")
                        .attr("width", width + margin.left + margin.right)
                        .attr("height", height + margin.top + margin.bottom)
                        .style("overflow", "visible")
                        .style("margin", "0 auto")
                        .style("display", "block");

                    const g = svg.append("g")
                        .attr("transform", `translate(${margin.left},${margin.top})`);

                    const x = d3.scaleTime()
                        .domain(d3.extent(data, d => d.date))
                        .range([0, width]);

                    const y = d3.scaleLinear()
                        .domain([0, d3.max(data, d => d.tokens)])
                        .nice()
                        .range([height, 0]);

                    const line = d3.line()
                        .x(d => x(d.date))
                        .y(d => y(d.tokens))
                        .curve(d3.curveCardinal);

                    // 添加渐变
                    const gradient = svg.append("defs")
                        .append("linearGradient")
                        .attr("id", "tokenGradient")
                        .attr("gradientUnits", "userSpaceOnUse")
                        .attr("x1", 0).attr("y1", height)
                        .attr("x2", 0).attr("y2", 0);

                    gradient.append("stop")
                        .attr("offset", "0%")
                        .attr("stop-color", "#8B5CF6")
                        .attr("stop-opacity", 0.1);

                    gradient.append("stop")
                        .attr("offset", "100%")
                        .attr("stop-color", "#8B5CF6")
                        .attr("stop-opacity", 0.6);

                    // 添加网格线
                    g.append("g")
                        .attr("class", "grid")
                        .call(d3.axisLeft(y)
                            .ticks(5)
                            .tickSize(-width)
                            .tickFormat("")
                        )
                        .style("stroke-dasharray", "3,3")
                        .style("opacity", 0.3);

                    // 添加面积
                    const area = d3.area()
                        .x(d => x(d.date))
                        .y0(height)
                        .y1(d => y(d.tokens))
                        .curve(d3.curveCardinal);

                    g.append("path")
                        .datum(data)
                        .attr("fill", "url(#tokenGradient)")
                        .attr("d", area)
                        .style("opacity", 0)
                        .transition()
                        .duration(1500)
                        .style("opacity", 1);

                    // 添加线条
                    const path = g.append("path")
                        .datum(data)
                        .attr("fill", "none")
                        .attr("stroke", "#8B5CF6")
                        .attr("stroke-width", 3)
                        .attr("d", line)
                        .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))");

                    // 线条动画
                    const totalLength = path.node().getTotalLength();
                    path
                        .attr("stroke-dasharray", totalLength + " " + totalLength)
                        .attr("stroke-dashoffset", totalLength)
                        .transition()
                        .duration(2000)
                        .attr("stroke-dashoffset", 0);

                    // 添加数据点
                    g.selectAll(".dot")
                        .data(data)
                        .enter().append("circle")
                        .attr("class", "dot")
                        .attr("cx", d => x(d.date))
                        .attr("cy", d => y(d.tokens))
                        .attr("r", 0)
                        .attr("fill", "#8B5CF6")
                        .attr("stroke", "white")
                        .attr("stroke-width", 2)
                        .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.2))")
                        .transition()
                        .duration(1000)
                        .delay((d, i) => i * 200 + 1000)
                        .attr("r", 5);

                    // 添加数值标签
                    g.selectAll(".label")
                        .data(data)
                        .enter().append("text")
                        .attr("class", "label")
                        .attr("x", d => x(d.date))
                        .attr("y", d => y(d.tokens) - 15)
                        .attr("text-anchor", "middle")
                        .style("font-size", "10px")
                        .style("font-weight", "bold")
                        .style("fill", "#374151")
                        .style("opacity", 0)
                        .text(d => d.tokens.toLocaleString())
                        .transition()
                        .duration(1000)
                        .delay((d, i) => i * 200 + 1500)
                        .style("opacity", 1);

                    // X轴
                    g.append("g")
                        .attr("transform", `translate(0,${height})`)
                        .call(d3.axisBottom(x).tickFormat(d3.timeFormat("%m/%d")))
                        .selectAll("text")
                        .style("font-size", "10px")
                        .style("fill", "#6B7280");

                    // Y轴
                    g.append("g")
                        .call(d3.axisLeft(y).ticks(5).tickFormat(d => d.toLocaleString()))
                        .selectAll("text")
                        .style("font-size", "10px")
                        .style("fill", "#6B7280");
                },

                // 创建各分组Token数统计图
                createGroupTokenChart() {
                    const container = d3.select("#groupTokenChart");
                    container.selectAll("*").remove();

                    // 统计各分组的Token使用情况
                    const groupTokens = {};
                    this.logs.forEach(log => {
                        if (log.provider_group && log.tokens_used && log.tokens_used > 0) {
                            groupTokens[log.provider_group] = (groupTokens[log.provider_group] || 0) + log.tokens_used;
                        }
                    });

                    const data = Object.entries(groupTokens)
                        .map(([group, tokens]) => ({
                            group: group.length > 12 ? group.substring(0, 12) + '...' : group,
                            fullGroup: group,
                            tokens
                        }))
                        .sort((a, b) => b.tokens - a.tokens)
                        .slice(0, 8); // 只显示前8个分组

                    if (data.length === 0) {
                        container.append("div")
                            .style("display", "flex")
                            .style("align-items", "center")
                            .style("justify-content", "center")
                            .style("height", "200px")
                            .style("color", "#9CA3AF")
                            .style("font-size", "14px")
                            .text("暂无分组Token数据");
                        return;
                    }

                    const margin = { top: 20, right: 20, bottom: 70, left: 70 };
                    const width = 350 - margin.left - margin.right;
                    const height = 220 - margin.top - margin.bottom;

                    const svg = container.append("svg")
                        .attr("width", width + margin.left + margin.right)
                        .attr("height", height + margin.top + margin.bottom)
                        .style("overflow", "visible")
                        .style("margin", "0 auto")
                        .style("display", "block");

                    const g = svg.append("g")
                        .attr("transform", `translate(${margin.left},${margin.top})`);

                    const x = d3.scaleBand()
                        .domain(data.map(d => d.group))
                        .range([0, width])
                        .padding(0.2);

                    const y = d3.scaleLinear()
                        .domain([0, d3.max(data, d => d.tokens)])
                        .nice()
                        .range([height, 0]);

                    // 创建渐变色
                    const colors = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#EC4899", "#06B6D4", "#84CC16"];

                    // 添加网格线
                    g.append("g")
                        .attr("class", "grid")
                        .call(d3.axisLeft(y)
                            .ticks(5)
                            .tickSize(-width)
                            .tickFormat("")
                        )
                        .style("stroke-dasharray", "3,3")
                        .style("opacity", 0.3);

                    // 添加柱状图
                    g.selectAll(".bar")
                        .data(data)
                        .enter().append("rect")
                        .attr("class", "bar")
                        .attr("x", d => x(d.group))
                        .attr("width", x.bandwidth())
                        .attr("y", height)
                        .attr("height", 0)
                        .attr("fill", (d, i) => colors[i % colors.length])
                        .attr("rx", 4)
                        .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")
                        .transition()
                        .duration(1000)
                        .delay((d, i) => i * 150)
                        .attr("y", d => y(d.tokens))
                        .attr("height", d => height - y(d.tokens));

                    // 添加数值标签
                    g.selectAll(".label")
                        .data(data)
                        .enter().append("text")
                        .attr("class", "label")
                        .attr("x", d => x(d.group) + x.bandwidth() / 2)
                        .attr("y", d => y(d.tokens) - 8)
                        .attr("text-anchor", "middle")
                        .style("font-size", "10px")
                        .style("font-weight", "bold")
                        .style("fill", "#374151")
                        .style("opacity", 0)
                        .text(d => d.tokens.toLocaleString())
                        .transition()
                        .duration(1000)
                        .delay((d, i) => i * 150 + 500)
                        .style("opacity", 1);

                    // X轴
                    g.append("g")
                        .attr("transform", `translate(0,${height})`)
                        .call(d3.axisBottom(x))
                        .selectAll("text")
                        .style("font-size", "9px")
                        .style("fill", "#6B7280")
                        .attr("transform", "rotate(-45)")
                        .style("text-anchor", "end");

                    // Y轴
                    g.append("g")
                        .call(d3.axisLeft(y).ticks(5).tickFormat(d => d.toLocaleString()))
                        .selectAll("text")
                        .style("font-size", "10px")
                        .style("fill", "#6B7280");

                    // 添加Y轴标签
                    g.append("text")
                        .attr("transform", "rotate(-90)")
                        .attr("y", 0 - margin.left)
                        .attr("x", 0 - (height / 2))
                        .attr("dy", "1em")
                        .style("text-anchor", "middle")
                        .style("font-size", "12px")
                        .style("fill", "#6B7280")
                        .text("Token数量");
                }
            }
        }
    </script>
</body>
</html>