# 代理密钥分组间请求设置功能实现总结

## 功能概述

为有多个允许分组的代理密钥增加了分组间请求设置功能，支持以下特性：

1. **分组间轮询** - 在允许的分组之间轮流分配请求
2. **按权重请求分组** - 根据配置的权重比例分配请求
3. **随机选择** - 随机选择分组
4. **故障转移** - 按优先级顺序选择分组

## 实现的文件和功能

### 核心文件

1. **internal/proxykey/manager.go**
   - 扩展了 `ProxyKey` 结构，添加了 `GroupSelectionConfig` 字段
   - 添加了分组选择器管理功能
   - 新增 `GenerateKeyWithConfig` 和 `UpdateKeyWithConfig` 方法
   - 新增 `SelectGroupForKey` 和 `GetGroupUsageStats` 方法

2. **internal/proxykey/group_selector.go** (新文件)
   - 实现了 `GroupSelector` 分组选择器
   - 支持四种选择策略：轮询、权重、随机、故障转移
   - 提供使用统计功能
   - 线程安全的实现

3. **internal/router/provider_router.go**
   - 扩展了 `RouteRequest` 结构，添加了 `ProxyKeyID` 字段
   - 新增 `NewProviderRouterWithProxyKey` 构造函数
   - 修改路由逻辑以支持代理密钥的分组选择

4. **internal/proxy/multi_provider_proxy.go**
   - 扩展了 `MultiProviderProxy` 结构，添加了代理密钥管理器支持
   - 新增 `NewMultiProviderProxyWithProxyKey` 构造函数
   - 修改请求处理逻辑以传递代理密钥ID

### 数据库支持

5. **internal/logger/database.go**
   - 更新了 `proxy_keys` 表结构，添加了 `group_selection_config` 字段
   - 更新了相关的数据库操作方法

6. **internal/logger/models.go**
   - 更新了 `ProxyKey` 模型，添加了分组选择配置字段

### API接口

7. **internal/api/multi_provider_server.go**
   - 更新了代理密钥生成和更新接口，支持分组选择配置
   - 新增了分组使用统计查询接口 `/admin/proxy-keys/:id/group-stats`

### 测试和文档

8. **internal/proxykey/group_selector_test.go** (新文件)
   - 完整的单元测试覆盖所有选择策略
   - 测试配置更新和边界情况

9. **docs/proxy-key-group-selection.md** (新文件)
   - 详细的功能说明和使用示例
   - API接口文档和配置说明

## 数据结构

### GroupSelectionConfig
```go
type GroupSelectionConfig struct {
    Strategy     GroupSelectionStrategy `json:"strategy"`      // 选择策略
    GroupWeights []GroupWeight          `json:"group_weights"` // 分组权重配置
}
```

### GroupWeight
```go
type GroupWeight struct {
    GroupID string `json:"group_id"` // 分组ID
    Weight  int    `json:"weight"`   // 权重值
}
```

### 选择策略
- `GroupSelectionRoundRobin` - 轮询
- `GroupSelectionWeighted` - 权重
- `GroupSelectionRandom` - 随机
- `GroupSelectionFailover` - 故障转移

## API接口变更

### 创建代理密钥
```http
POST /admin/proxy-keys
Content-Type: application/json

{
  "name": "测试密钥",
  "description": "支持多分组的密钥",
  "allowedGroups": ["group1", "group2"],
  "groupSelectionConfig": {
    "strategy": "weighted",
    "groupWeights": [
      {"groupId": "group1", "weight": 3},
      {"groupId": "group2", "weight": 1}
    ]
  }
}
```

### 更新代理密钥
```http
PUT /admin/proxy-keys/:id
Content-Type: application/json

{
  "name": "更新后的密钥",
  "description": "修改配置",
  "is_active": true,
  "allowedGroups": ["group1", "group2", "group3"],
  "groupSelectionConfig": {
    "strategy": "round_robin"
  }
}
```

### 获取分组使用统计
```http
GET /admin/proxy-keys/:id/group-stats
```

## 默认行为

1. 如果代理密钥只有一个允许分组，不会创建分组选择器
2. 如果有多个允许分组但未指定配置，默认使用轮询策略
3. 权重策略中未配置权重的分组默认权重为1
4. 分组选择器会跟踪使用统计（使用次数和最后使用时间）

## 测试验证

- 轮询策略：验证按顺序循环选择分组
- 权重策略：验证按权重比例分配请求（测试1000次，权重3:1的分组大约获得75%:25%的请求）
- 随机策略：验证所有分组都能被选中
- 故障转移策略：验证总是选择第一个分组
- 配置更新：验证运行时配置更新生效
- 边界情况：验证空分组列表和单分组的处理

## 兼容性

- 向后兼容：现有的代理密钥继续正常工作
- 数据库迁移：新增字段使用默认值，不影响现有数据
- API兼容：现有API接口保持不变，新增可选字段

这个功能为TurnsAPI提供了强大的负载分配和成本优化能力，用户可以根据不同的业务需求灵活配置分组间的请求分配策略。
