package main

import (
	"fmt"
	"log"
	"turnsapi/internal"
	"turnsapi/internal/logger"
	"turnsapi/internal/proxykey"
)

func main() {
	fmt.Println("=== 调试代理密钥加载问题 ===")

	// 创建测试配置
	config := &internal.Config{
		Database: internal.DatabaseConfig{
			Path: "data/turnsapi.db",
		},
		UserGroups: map[string]*internal.UserGroup{
			"test_group": {
				Name:         "Test Group",
				ProviderType: "openai",
				BaseURL:      "https://api.openai.com/v1",
				Enabled:      true,
				APIKeys:      []string{"test-key"},
				Models:       []string{"gpt-3.5-turbo"},
			},
		},
	}

	// 1. 直接检查数据库
	fmt.Println("\n1. 直接检查数据库中的代理密钥:")
	requestLogger, err := logger.NewRequestLogger(config.Database.Path)
	if err != nil {
		log.Fatalf("Failed to create request logger: %v", err)
	}
	defer requestLogger.Close()

	dbKeys, err := requestLogger.GetAllProxyKeys()
	if err != nil {
		log.Fatalf("Failed to get proxy keys from database: %v", err)
	}

	fmt.Printf("数据库中找到 %d 个代理密钥:\n", len(dbKeys))
	for i, key := range dbKeys {
		fmt.Printf("  %d. ID: %s, Name: %s, Key: %s, Active: %t\n", 
			i+1, key.ID, key.Name, key.Key, key.IsActive)
		fmt.Printf("     Allowed Groups: %v\n", key.AllowedGroups)
		fmt.Printf("     Group Selection Config: %s\n", key.GroupSelectionConfig)
	}

	// 2. 测试代理密钥管理器加载
	fmt.Println("\n2. 测试代理密钥管理器加载:")
	configManager := internal.NewConfigManager(config)
	
	// 创建配置提供者适配器
	configProvider := &configManagerAdapter{configManager: configManager}
	
	// 创建代理密钥管理器
	fmt.Println("创建代理密钥管理器...")
	proxyKeyManager := proxykey.NewManagerWithConfig(requestLogger, configProvider)
	
	// 获取内存中的密钥
	memoryKeys := proxyKeyManager.GetAllKeys()
	fmt.Printf("内存中找到 %d 个代理密钥:\n", len(memoryKeys))
	for i, key := range memoryKeys {
		fmt.Printf("  %d. ID: %s, Name: %s, Key: %s, Active: %t\n", 
			i+1, key.ID, key.Name, key.Key, key.IsActive)
	}

	// 3. 比较结果
	fmt.Println("\n3. 结果比较:")
	if len(dbKeys) != len(memoryKeys) {
		fmt.Printf("❌ 数据库中有 %d 个密钥，但内存中只有 %d 个密钥\n", len(dbKeys), len(memoryKeys))
		fmt.Println("这表明加载过程中出现了问题")
	} else {
		fmt.Printf("✅ 数据库和内存中的密钥数量一致: %d 个\n", len(dbKeys))
	}

	fmt.Println("\n=== 调试完成 ===")
}

// configManagerAdapter 配置管理器适配器
type configManagerAdapter struct {
	configManager *internal.ConfigManager
}

// GetEnabledGroups 实现ConfigProvider接口
func (cma *configManagerAdapter) GetEnabledGroups() map[string]interface{} {
	enabledGroups := cma.configManager.GetEnabledGroups()
	result := make(map[string]interface{})
	for groupID := range enabledGroups {
		result[groupID] = struct{}{}
	}
	return result
}
