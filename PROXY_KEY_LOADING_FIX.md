# 代理密钥启动加载问题修复

## 问题描述

程序启动时，已生成的代理密钥被清空了，导致用户需要重新创建代理密钥。

## 根本原因分析

经过分析，发现了两个主要问题：

### 1. 请求日志记录器创建失败处理不当

在 `internal/api/multi_provider_server.go` 第90-94行：

```go
// 创建请求日志记录器
requestLogger, err := logger.NewRequestLogger(config.Database.Path)
if err != nil {
    log.Printf("Failed to create request logger: %v", err)  // 只记录日志，继续执行
}
```

**问题：** 如果 `requestLogger` 创建失败，它会被设置为 `nil`，但程序继续执行。这导致代理密钥管理器在 `loadKeysFromDB()` 方法中跳过加载过程：

```go
func (m *Manager) loadKeysFromDB() error {
    if m.requestLogger == nil {
        return nil  // 直接返回，跳过加载
    }
    // ...
}
```

### 2. 多提供商代理构造函数使用错误

在 `internal/api/multi_provider_server.go` 第113行：

```go
// 错误的构造函数调用
server.proxy = proxy.NewMultiProviderProxy(config, keyManager, requestLogger)
```

**问题：** 使用了不带代理密钥管理器的构造函数，应该使用 `NewMultiProviderProxyWithProxyKey`。

## 修复方案

### 1. 修复请求日志记录器错误处理

**修改前：**
```go
requestLogger, err := logger.NewRequestLogger(config.Database.Path)
if err != nil {
    log.Printf("Failed to create request logger: %v", err)
}
```

**修改后：**
```go
requestLogger, err := logger.NewRequestLogger(config.Database.Path)
if err != nil {
    return nil, fmt.Errorf("failed to create request logger: %w", err)
}
```

**效果：** 确保请求日志记录器创建成功，否则服务器启动失败，避免 `nil` 导致的问题。

### 2. 修复多提供商代理构造函数

**修改前：**
```go
server.proxy = proxy.NewMultiProviderProxy(config, keyManager, requestLogger)
```

**修改后：**
```go
server.proxy = proxy.NewMultiProviderProxyWithProxyKey(config, keyManager, proxyKeyManager, requestLogger)
```

**效果：** 确保多提供商代理使用正确的构造函数，包含代理密钥管理器。

### 3. 添加调试日志

在 `internal/proxykey/manager.go` 的 `loadKeysFromDB` 方法中添加了详细的调试日志：

```go
func (m *Manager) loadKeysFromDB() error {
    if m.requestLogger == nil {
        log.Println("Warning: requestLogger is nil, skipping proxy key loading")
        return nil
    }

    log.Println("Loading proxy keys from database...")
    dbKeys, err := m.requestLogger.GetAllProxyKeys()
    if err != nil {
        return fmt.Errorf("failed to get proxy keys from database: %w", err)
    }

    log.Printf("Found %d proxy keys in database", len(dbKeys))
    
    // ... 加载逻辑 ...
    
    for _, dbKey := range dbKeys {
        // ... 处理每个密钥 ...
        log.Printf("Loaded proxy key: %s (%s)", key.Name, key.ID)
    }
    
    log.Printf("Successfully loaded %d proxy keys from database", len(m.keys))
    return nil
}
```

## 修复验证

### 启动日志检查

修复后，服务器启动时应该看到类似的日志：

```
Loading proxy keys from database...
Found 3 proxy keys in database
Loaded proxy key: Test Key 1 (tapi-abc123...)
Loaded proxy key: Test Key 2 (tapi-def456...)
Loaded proxy key: Test Key 3 (tapi-ghi789...)
Successfully loaded 3 proxy keys from database
```

### 功能验证

1. **代理密钥持久化**：重启服务器后，之前创建的代理密钥应该仍然存在
2. **日志记录功能**：模型请求日志统计功能应该正常工作
3. **分组间请求设置**：支持空分组列表（访问所有分组）的配置

## 相关文件修改

### 修改的文件

1. **internal/api/multi_provider_server.go**
   - 修复请求日志记录器错误处理
   - 修复多提供商代理构造函数调用

2. **internal/proxykey/manager.go**
   - 添加详细的调试日志
   - 修复日志输出中的变量引用

### 影响范围

- **向后兼容**：修复不影响现有功能
- **数据安全**：不会丢失现有的代理密钥数据
- **功能增强**：恢复了日志统计功能

## 预防措施

### 1. 错误处理改进

确保关键组件（如数据库连接、日志记录器）创建失败时，服务器启动失败而不是继续运行。

### 2. 启动检查

添加启动时的健康检查，验证关键功能是否正常工作。

### 3. 日志监控

通过启动日志监控代理密钥加载情况，及时发现问题。

## 总结

通过修复请求日志记录器的错误处理和多提供商代理的构造函数调用，解决了代理密钥在程序启动时被清空的问题。现在：

- ✅ 代理密钥在重启后正确加载
- ✅ 模型请求日志统计功能恢复
- ✅ 支持空分组列表的分组间请求设置
- ✅ 添加了详细的调试日志便于问题排查

这些修复确保了代理密钥管理功能的稳定性和可靠性。
