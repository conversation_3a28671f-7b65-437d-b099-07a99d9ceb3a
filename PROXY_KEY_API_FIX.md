# 代理密钥API 500错误修复

## 问题描述

在访问 `POST http://localhost:8080/admin/proxy-keys` 时遇到500内部服务器错误，这是由于数据库表缺少新增的 `group_selection_config` 字段导致的。

## 根本原因

当我们添加了分组间请求设置功能时，在 `proxy_keys` 表中新增了 `group_selection_config` 字段，但现有的数据库可能没有这个字段，导致插入操作失败。

## 修复方案

### 1. 数据库迁移增强

在 `internal/logger/database.go` 的 `migrateDatabase()` 方法中添加了对 `group_selection_config` 字段的检查和自动添加：

```go
// 检查proxy_keys表是否有group_selection_config列
err = d.db.QueryRow(`
    SELECT COUNT(*) > 0
    FROM pragma_table_info('proxy_keys')
    WHERE name = 'group_selection_config'
`).Scan(&columnExists)

if err != nil {
    return fmt.Errorf("failed to check group_selection_config column existence: %w", err)
}

// 如果列不存在，添加它
if !columnExists {
    log.Println("Adding group_selection_config column to proxy_keys table...")
    _, err = d.db.Exec(`ALTER TABLE proxy_keys ADD COLUMN group_selection_config TEXT`)
    if err != nil {
        return fmt.Errorf("failed to add group_selection_config column: %w", err)
    }
    log.Println("Successfully added group_selection_config column")
}
```

### 2. 自动迁移机制

- **检测机制**: 使用 SQLite 的 `pragma_table_info` 检查字段是否存在
- **自动添加**: 如果字段不存在，自动执行 `ALTER TABLE` 添加字段
- **日志记录**: 记录迁移过程，便于调试和监控

### 3. 向后兼容

- **新字段可选**: `group_selection_config` 字段允许为 NULL
- **默认行为**: 如果没有配置，系统使用默认的轮询策略
- **现有数据**: 不影响现有的代理密钥数据

## 测试验证

### 数据库迁移测试
```
Testing database migration...
✓ Database created and migrated successfully
✓ Proxy key with group selection config inserted successfully
✓ Retrieved proxy key: Test Key
  Allowed Groups: [group1 group2]
  Group Selection Config: {"strategy":"weighted","group_weights":[...]}
```

### 功能验证
- ✅ 数据库表自动迁移
- ✅ 新字段正确添加
- ✅ 代理密钥创建成功
- ✅ 分组选择配置正确存储和读取

## 部署说明

### 自动迁移
当服务启动时，数据库迁移会自动执行：

1. **检查现有表结构**
2. **识别缺失字段**
3. **自动添加新字段**
4. **记录迁移日志**

### 日志输出
如果需要添加字段，会看到类似日志：
```
Adding group_selection_config column to proxy_keys table...
Successfully added group_selection_config column
```

### 零停机时间
- 迁移过程不影响现有功能
- 现有代理密钥继续正常工作
- 新功能立即可用

## API使用示例

### 创建单分组代理密钥
```json
POST /admin/proxy-keys
{
    "name": "Single Group Key",
    "description": "Key for single group",
    "allowedGroups": ["openai_group"]
}
```

### 创建多分组代理密钥（轮询）
```json
POST /admin/proxy-keys
{
    "name": "Multi Group Round Robin",
    "description": "Key with round robin strategy",
    "allowedGroups": ["openai_group", "claude_group"],
    "groupSelectionConfig": {
        "strategy": "round_robin"
    }
}
```

### 创建多分组代理密钥（权重）
```json
POST /admin/proxy-keys
{
    "name": "Multi Group Weighted",
    "description": "Key with weighted strategy",
    "allowedGroups": ["openai_group", "claude_group"],
    "groupSelectionConfig": {
        "strategy": "weighted",
        "groupWeights": [
            {"group_id": "openai_group", "weight": 3},
            {"group_id": "claude_group", "weight": 1}
        ]
    }
}
```

## 故障排除

### 如果仍然遇到500错误

1. **检查日志**: 查看服务器日志中的具体错误信息
2. **手动迁移**: 如果自动迁移失败，可以手动执行SQL：
   ```sql
   ALTER TABLE proxy_keys ADD COLUMN group_selection_config TEXT;
   ```
3. **重启服务**: 确保迁移逻辑被执行
4. **数据库权限**: 确保应用有修改数据库表结构的权限

### 常见问题

**Q: 现有的代理密钥会受影响吗？**
A: 不会，现有密钥继续正常工作，新字段为可选字段。

**Q: 如何验证迁移是否成功？**
A: 查看服务启动日志，或使用数据库工具检查表结构。

**Q: 可以回滚吗？**
A: 可以，但需要手动删除新增字段（不推荐，因为会丢失配置数据）。

## 总结

通过添加自动数据库迁移机制，解决了代理密钥API的500错误问题。现在系统可以：

- 自动检测和添加缺失的数据库字段
- 保持向后兼容性
- 支持新的分组间请求设置功能
- 提供零停机时间的升级体验

修复后，代理密钥创建API应该能够正常工作，支持单分组和多分组的各种配置策略。
