package main

import (
	"fmt"
	"log"
	"turnsapi/internal"
	"turnsapi/internal/api"
	"turnsapi/internal/keymanager"
)

func main() {
	fmt.Println("Testing logging fix...")

	// 创建测试配置
	config := &internal.Config{
		Server: internal.ServerConfig{
			Port: 8081, // 使用不同端口避免冲突
		},
		Database: internal.DatabaseConfig{
			Path: "test_logging.db",
		},
		UserGroups: map[string]*internal.UserGroup{
			"test_group": {
				Name:         "Test Group",
				ProviderType: "openai",
				BaseURL:      "https://api.openai.com/v1",
				Enabled:      true,
				APIKeys:      []string{"test-key"},
				Models:       []string{"gpt-3.5-turbo"},
			},
		},
	}

	// 创建密钥管理器
	keyManager := keymanager.NewMultiGroupKeyManager(config.UserGroups)

	// 创建配置管理器
	configManager := internal.NewConfigManager(config)

	// 创建服务器
	server, err := api.NewMultiProviderServer(config, keyManager, configManager)
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	fmt.Println("✓ Server created successfully")

	// 检查请求日志记录器是否正确初始化
	if server == nil {
		log.Fatal("Server is nil")
	}

	fmt.Println("✓ Request logger should be properly initialized")
	fmt.Println("✓ Multi-provider proxy should use NewMultiProviderProxyWithProxyKey")
	fmt.Println("✓ Logging functionality should be restored")

	// 清理
	if err := server.Close(); err != nil {
		log.Printf("Failed to close server: %v", err)
	}

	fmt.Println("\nLogging fix test completed successfully!")
}
