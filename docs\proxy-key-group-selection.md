# 代理密钥分组间请求设置

本文档介绍如何为有多个允许分组的代理密钥配置分组间请求设置，包括分组间轮询和按权重请求分组。

## 功能概述

当一个代理密钥被配置为可以访问多个分组时，系统需要决定如何在这些分组之间分配请求。新的分组间请求设置提供了以下策略：

1. **轮询（Round Robin）** - 在允许的分组之间轮流分配请求
2. **权重（Weighted）** - 根据配置的权重比例分配请求
3. **随机（Random）** - 随机选择分组
4. **故障转移（Failover）** - 按优先级顺序选择分组

## 配置结构

### 分组选择策略

```json
{
  "strategy": "round_robin|weighted|random|failover",
  "groupWeights": [
    {
      "groupId": "group1",
      "weight": 3
    },
    {
      "groupId": "group2", 
      "weight": 1
    }
  ]
}
```

### 策略说明

#### 1. 轮询策略（round_robin）
```json
{
  "strategy": "round_robin"
}
```
- 在允许的分组之间按顺序轮流分配请求
- 确保每个分组获得相等的请求数量
- 适用于负载均衡场景

#### 2. 权重策略（weighted）
```json
{
  "strategy": "weighted",
  "groupWeights": [
    {
      "groupId": "openai_group",
      "weight": 3
    },
    {
      "groupId": "claude_group",
      "weight": 1
    }
  ]
}
```
- 根据权重比例分配请求
- 权重越高，获得的请求越多
- 上例中openai_group将获得75%的请求，claude_group获得25%
- 适用于不同分组有不同处理能力的场景

#### 3. 随机策略（random）
```json
{
  "strategy": "random"
}
```
- 随机选择允许的分组
- 长期来看请求分布趋于均匀
- 适用于简单的负载分散

#### 4. 故障转移策略（failover）
```json
{
  "strategy": "failover"
}
```
- 按allowedGroups的顺序优先选择分组
- 通常选择第一个可用的分组
- 适用于主备分组的场景

## API 使用示例

### 创建带分组选择配置的代理密钥

```bash
curl -X POST http://localhost:8080/admin/proxy-keys \
  -H "Content-Type: application/json" \
  -d '{
    "name": "多分组测试密钥",
    "description": "支持OpenAI和Claude分组的密钥",
    "allowedGroups": ["openai_group", "claude_group"],
    "groupSelectionConfig": {
      "strategy": "weighted",
      "groupWeights": [
        {
          "groupId": "openai_group",
          "weight": 3
        },
        {
          "groupId": "claude_group",
          "weight": 1
        }
      ]
    }
  }'
```

### 更新代理密钥的分组选择配置

```bash
curl -X PUT http://localhost:8080/admin/proxy-keys/{keyId} \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的密钥",
    "description": "修改为轮询策略",
    "is_active": true,
    "allowedGroups": ["openai_group", "claude_group", "gemini_group"],
    "groupSelectionConfig": {
      "strategy": "round_robin"
    }
  }'
```

### 获取分组使用统计

```bash
curl -X GET http://localhost:8080/admin/proxy-keys/{keyId}/group-stats
```

响应示例：
```json
{
  "success": true,
  "stats": {
    "openai_group": {
      "groupId": "openai_group",
      "usageCount": 150,
      "lastUsed": "2024-01-15T10:30:00Z"
    },
    "claude_group": {
      "groupId": "claude_group", 
      "usageCount": 50,
      "lastUsed": "2024-01-15T10:25:00Z"
    }
  }
}
```

## 默认行为

- 如果代理密钥只有一个允许分组，不会创建分组选择器
- 如果代理密钥有多个允许分组但未指定分组选择配置，默认使用轮询策略
- 分组选择器会跟踪每个分组的使用统计，包括使用次数和最后使用时间

## 注意事项

1. **权重配置**：在权重策略中，如果某个分组没有配置权重，默认权重为1
2. **分组可用性**：系统会自动检查分组是否启用，只在启用的分组中进行选择
3. **统计持久化**：分组使用统计目前存储在内存中，服务重启后会重置
4. **性能考虑**：分组选择操作是线程安全的，但在高并发场景下可能成为性能瓶颈

## 使用场景

1. **成本优化**：将大部分请求路由到成本较低的分组，少部分路由到高质量分组
2. **负载均衡**：在多个相同类型的分组之间均匀分配负载
3. **A/B测试**：按比例将请求分配到不同的模型分组进行测试
4. **故障恢复**：主分组不可用时自动切换到备用分组
