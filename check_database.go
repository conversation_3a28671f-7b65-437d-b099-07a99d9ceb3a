package main

import (
	"fmt"
	"log"
	"os"
	"turnsapi/internal/logger"
)

func main() {
	fmt.Println("=== 检查数据库状态 ===")

	// 检查默认数据库路径
	dbPath := "data/turnsapi.db"
	
	// 检查文件是否存在
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		fmt.Printf("❌ 数据库文件不存在: %s\n", dbPath)
		
		// 检查data目录是否存在
		if _, err := os.Stat("data"); os.IsNotExist(err) {
			fmt.Println("❌ data目录也不存在")
		} else {
			fmt.Println("✅ data目录存在")
			// 列出data目录中的文件
			files, err := os.ReadDir("data")
			if err != nil {
				fmt.Printf("❌ 无法读取data目录: %v\n", err)
			} else {
				fmt.Printf("data目录中的文件:\n")
				for _, file := range files {
					fmt.Printf("  - %s\n", file.Name())
				}
			}
		}
		return
	}

	fmt.Printf("✅ 数据库文件存在: %s\n", dbPath)

	// 获取文件信息
	fileInfo, err := os.Stat(dbPath)
	if err != nil {
		fmt.Printf("❌ 无法获取文件信息: %v\n", err)
		return
	}

	fmt.Printf("文件大小: %d bytes\n", fileInfo.Size())
	fmt.Printf("修改时间: %s\n", fileInfo.ModTime().Format("2006-01-02 15:04:05"))

	// 尝试连接数据库
	fmt.Println("\n=== 尝试连接数据库 ===")
	db, err := logger.NewDatabase(dbPath)
	if err != nil {
		fmt.Printf("❌ 无法连接数据库: %v\n", err)
		return
	}
	defer db.Close()

	fmt.Println("✅ 数据库连接成功")

	// 查询代理密钥
	fmt.Println("\n=== 查询代理密钥 ===")
	keys, err := db.GetAllProxyKeys()
	if err != nil {
		fmt.Printf("❌ 查询代理密钥失败: %v\n", err)
		return
	}

	fmt.Printf("找到 %d 个代理密钥:\n", len(keys))
	for i, key := range keys {
		fmt.Printf("%d. ID: %s\n", i+1, key.ID)
		fmt.Printf("   Name: %s\n", key.Name)
		fmt.Printf("   Key: %s\n", key.Key)
		fmt.Printf("   Active: %t\n", key.IsActive)
		fmt.Printf("   Allowed Groups: %v\n", key.AllowedGroups)
		fmt.Printf("   Group Selection Config: %s\n", key.GroupSelectionConfig)
		fmt.Printf("   Created: %s\n", key.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Println()
	}

	if len(keys) == 0 {
		fmt.Println("⚠️  数据库中没有代理密钥，这可能是问题的根源")
	} else {
		fmt.Printf("✅ 数据库中有 %d 个代理密钥，数据库本身没有问题\n", len(keys))
	}

	fmt.Println("\n=== 检查完成 ===")
}
